<?php

namespace Drupal\Tests\bw_trophies\Kernel;

use <PERSON><PERSON>al\bw_nanoboost_progress\Entity\NanoBoostProgress;
use <PERSON><PERSON><PERSON>\bw_trophies\Entity\Trophy;
use <PERSON><PERSON>al\bw_trophies\Entity\TrophyType;
use <PERSON><PERSON><PERSON>\bw_trophies\Plugin\QueueWorker\TrophiesQueueWorker;
use Drupal\Core\Database\Database;
use Dr<PERSON>al\KernelTests\KernelTestBase;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\node\Entity\NodeType;
use Drupal\profile\Entity\Profile;
use Drupal\profile\Entity\ProfileType;
use Dr<PERSON>al\user\Entity\User;

/**
 * Tests the TrophiesQueueWorker calculateBraynAgent method.
 *
 * @group bw_trophies
 */
class TrophiesQueueWorkerBraynAgentTest extends KernelTestBase {

  /**
   * Modules to enable.
   *
   * @var array
   */
  protected static $modules = [
    'system',
    'user',
    'field',
    'node',
    'profile',
    'bw_trophies',
    'bw_nanoboost_progress',
    'bw_notifications',
    'paragraphs',
    'entity_reference_revisions',
    'options',
    'text',
    'serialization',
    'language',
    'content_translation',
    'field_ui',
  ];

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  protected $connection;

  /**
   * Test user.
   *
   * @var \Drupal\user\Entity\User
   */
  protected $testUser;

  /**
   * Test user profile.
   *
   * @var \Drupal\profile\Entity\Profile
   */
  protected $testProfile;

  /**
   * Test shortzzz nodes.
   *
   * @var \Drupal\node\Entity\Node[]
   */
  protected $testNodes = [];

  /**
   * Trophy queue worker instance.
   *
   * @var \Drupal\bw_trophies\Plugin\QueueWorker\TrophiesQueueWorker
   */
  protected $queueWorker;

  /**
   * {@inheritdoc}
   */
  protected function setUp(): void {
    parent::setUp();

    $this->entityTypeManager = $this->container->get('entity_type.manager');
    $this->connection = Database::getConnection();

    // Install required schemas.
    $this->installEntitySchema('user');
    $this->installEntitySchema('node');
    $this->installEntitySchema('profile');
    $this->installEntitySchema('nanoboost_progress');
    $this->installEntitySchema('trophy');
    $this->installEntitySchema('trophy_type');
    $this->installEntitySchema('user_trophy');
    $this->installEntitySchema('field_storage_config');
    $this->installEntitySchema('field_config');
    $this->installSchema('system', ['sequences']);
    $this->installSchema('bw_notifications', ['bw_notifications_user_data']);

    // Create content types and profile types.
    $this->createContentTypes();
    $this->createProfileTypes();
    $this->createTrophyTypes();

    // Create test data.
    $this->createTestUser();
    $this->createTestNodes();
    $this->createTestTrophies();

    // Create queue worker instance.
    $this->queueWorker = $this->createQueueWorkerInstance();
  }

  /**
   * Creates required content types.
   */
  protected function createContentTypes(): void {
    // Create shortzzz content type.
    $shortzzz_type = NodeType::create([
      'type' => 'shortzzz',
      'name' => 'Shortzzz',
    ]);
    $shortzzz_type->save();

    // Create the field for shortzzz type.
    $field_storage = $this->entityTypeManager
      ->getStorage('field_storage_config')
      ->create([
        'field_name' => 'field_sz_type',
        'entity_type' => 'node',
        'type' => 'list_string',
        'settings' => [
          'allowed_values' => [
            'module' => 'Module',
            'lesson' => 'Lesson',
            'exercise' => 'Exercise',
          ],
        ],
      ]);
    $field_storage->save();

    $field = $this->entityTypeManager
      ->getStorage('field_config')
      ->create([
        'field_storage' => $field_storage,
        'bundle' => 'shortzzz',
        'label' => 'Shortzzz Type',
        'required' => TRUE,
      ]);
    $field->save();
  }

  /**
   * Creates required profile types.
   */
  protected function createProfileTypes(): void {
    // Create student profile type.
    $profile_type = ProfileType::create([
      'id' => 'student',
      'label' => 'Student',
    ]);
    $profile_type->save();
  }

  /**
   * Creates required trophy types.
   */
  protected function createTrophyTypes(): void {
    // Create activity trophy type.
    $trophy_type = TrophyType::create([
      'id' => 'activity_trophy',
      'label' => 'Activity Trophy',
    ]);
    $trophy_type->save();
  }

  /**
   * Creates test user and profile.
   */
  protected function createTestUser(): void {
    $this->testUser = User::create([
      'name' => 'testuser',
      'mail' => '<EMAIL>',
      'status' => 1,
    ]);
    $this->testUser->save();

    $this->testProfile = Profile::create([
      'type' => 'student',
      'uid' => $this->testUser->id(),
    ]);
    $this->testProfile->save();
  }

  /**
   * Creates test shortzzz nodes.
   */
  protected function createTestNodes(): void {
    // Create shortzzz nodes with different types.
    // First 10 nodes are "module" type (should count for trophies).
    for ($i = 1; $i <= 10; $i++) {
      $node = Node::create([
        'type' => 'shortzzz',
        'title' => "Test Module $i",
        'status' => 1,
        'uid' => $this->testUser->id(),
        'field_sz_type' => 'module',
      ]);
      $node->save();
      $this->testNodes[] = $node;
    }

    // Next 5 nodes are "lesson" type (should NOT count for trophies).
    for ($i = 11; $i <= 15; $i++) {
      $node = Node::create([
        'type' => 'shortzzz',
        'title' => "Test Lesson $i",
        'status' => 1,
        'uid' => $this->testUser->id(),
        'field_sz_type' => 'lesson',
      ]);
      $node->save();
      $this->testNodes[] = $node;
    }
  }

  /**
   * Creates test trophies.
   */
  protected function createTestTrophies(): void {
    $trophy_levels = ['junior', 'middle', 'senior'];

    foreach ($trophy_levels as $level) {
      $trophy = Trophy::create([
        'machine_name' => Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . "_{$level}",
        'title' => "Brayn Agent {$level}",
        'type' => 'activity_trophy',
        'status' => 1,
      ]);
      $trophy->save();
    }
  }

  /**
   * Creates a queue worker instance for testing.
   */
  protected function createQueueWorkerInstance(): TrophiesQueueWorker {
    $notification_service = $this->createMock('\Drupal\bw_notifications\NotificationService');

    return new TrophiesQueueWorker(
      [],
      'bw_trophies_queue',
      [],
      $this->entityTypeManager,
      $this->connection,
      $notification_service
    );
  }

  /**
   * Creates nanoboost progress entities.
   *
   * @param int $count
   *   Number of completed progress entities to create.
   */
  protected function createNanoboostProgress(int $count): void {
    for ($i = 0; $i < $count; $i++) {
      if (isset($this->testNodes[$i])) {
        $progress = NanoBoostProgress::create([
          'uid' => $this->testUser->id(),
          'nbid' => $this->testNodes[$i]->id(),
          'status' => 'completed',
          'data' => [],
          'position' => $i + 1,
        ]);
        $progress->save();
      }
    }
  }

  /**
   * Tests calculateBraynAgent with no completed modules.
   */
  public function testCalculateBraynAgentNoCompletedModules(): void {
    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that no trophies were awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll();

    $this->assertEmpty($user_trophies, 'No trophies should be awarded with 0 completed modules.');
  }

  /**
   * Tests calculateBraynAgent with 1 completed module (junior trophy).
   */
  public function testCalculateBraynAgentJuniorTrophy(): void {
    // Create 1 completed nanoboost progress.
    $this->createNanoboostProgress(1);

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that junior trophy was awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_trophy = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';
    $this->assertContains($expected_trophy, $user_trophies, 'Junior trophy should be awarded with 1 completed module.');
    $this->assertCount(1, $user_trophies, 'Only one trophy should be awarded.');
  }

  /**
   * Tests calculateBraynAgent with 3 completed modules (middle trophy).
   */
  public function testCalculateBraynAgentMiddleTrophy(): void {
    // Create 3 completed nanoboost progress.
    $this->createNanoboostProgress(3);

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that both junior and middle trophies were awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_junior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';
    $expected_middle = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_middle';

    $this->assertContains($expected_junior, $user_trophies, 'Junior trophy should be awarded.');
    $this->assertContains($expected_middle, $user_trophies, 'Middle trophy should be awarded with 3 completed modules.');
    $this->assertCount(2, $user_trophies, 'Two trophies should be awarded.');
  }

  /**
   * Tests calculateBraynAgent with 12 completed modules (senior trophy).
   */
  public function testCalculateBraynAgentSeniorTrophy(): void {
    // Create 12 completed nanoboost progress.
    $this->createNanoboostProgress(12);

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that all three trophies were awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_junior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';
    $expected_middle = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_middle';
    $expected_senior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_senior';

    $this->assertContains($expected_junior, $user_trophies, 'Junior trophy should be awarded.');
    $this->assertContains($expected_middle, $user_trophies, 'Middle trophy should be awarded.');
    $this->assertContains($expected_senior, $user_trophies, 'Senior trophy should be awarded with 12 completed modules.');
    $this->assertCount(3, $user_trophies, 'Three trophies should be awarded.');
  }

  /**
   * Tests that already achieved trophies are not awarded again.
   */
  public function testCalculateBraynAgentNoDoubleAward(): void {
    // Create 3 completed nanoboost progress.
    $this->createNanoboostProgress(3);

    // Manually insert junior trophy as already achieved.
    $this->connection->insert('bw_user_trophy')
      ->fields([
        'uid' => $this->testUser->id(),
        'trophy' => Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior',
        'date_acquired' => time(),
      ])
      ->execute();

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that only middle trophy was awarded (junior already exists).
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_junior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';
    $expected_middle = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_middle';

    $this->assertContains($expected_junior, $user_trophies, 'Junior trophy should exist.');
    $this->assertContains($expected_middle, $user_trophies, 'Middle trophy should be awarded.');
    $this->assertCount(2, $user_trophies, 'Only two trophies should exist (no duplicates).');
  }

  /**
   * Tests calculateBraynAgent with in_progress status (should not count).
   */
  public function testCalculateBraynAgentInProgressNotCounted(): void {
    // Create 1 completed and 2 in_progress nanoboost progress.
    $this->createNanoboostProgress(1);

    // Create in_progress entries.
    for ($i = 1; $i < 3; $i++) {
      if (isset($this->testNodes[$i])) {
        $progress = NanoBoostProgress::create([
          'uid' => $this->testUser->id(),
          'nbid' => $this->testNodes[$i]->id(),
          'status' => 'in_progress',
          'data' => [],
          'position' => $i + 1,
        ]);
        $progress->save();
      }
    }

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that only junior trophy was awarded (only 1 completed).
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_junior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';

    $this->assertContains($expected_junior, $user_trophies, 'Junior trophy should be awarded.');
    $this->assertCount(1, $user_trophies, 'Only one trophy should be awarded (in_progress not counted).');
  }

  /**
   * Tests calculateBraynAgent with specific trophy type processing.
   */
  public function testCalculateBraynAgentSpecificType(): void {
    // Create 3 completed nanoboost progress.
    $this->createNanoboostProgress(3);

    // Process the queue item with specific type.
    $this->queueWorker->processItem([
      'id' => $this->testUser->id(),
      'types' => [Trophy::TROPHY_ACTIVITY_BRAYN_AGENT],
    ]);

    // Check that both junior and middle trophies were awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_junior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';
    $expected_middle = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_middle';

    $this->assertContains($expected_junior, $user_trophies, 'Junior trophy should be awarded.');
    $this->assertContains($expected_middle, $user_trophies, 'Middle trophy should be awarded.');
    $this->assertCount(2, $user_trophies, 'Two trophies should be awarded.');
  }

  /**
   * Tests calculateBraynAgent with missing user profile.
   */
  public function testCalculateBraynAgentMissingProfile(): void {
    // Delete the user profile.
    $this->testProfile->delete();

    // Create completed nanoboost progress.
    $this->createNanoboostProgress(3);

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that no trophies were awarded due to missing profile.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll();

    $this->assertEmpty($user_trophies, 'No trophies should be awarded without user profile.');
  }

  /**
   * Tests calculateBraynAgent with non-existent user.
   */
  public function testCalculateBraynAgentNonExistentUser(): void {
    // Process the queue item with non-existent user ID.
    $this->queueWorker->processItem(['id' => 99999]);

    // Check that no trophies were awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', 99999)
      ->execute()
      ->fetchAll();

    $this->assertEmpty($user_trophies, 'No trophies should be awarded for non-existent user.');
  }

  /**
   * Tests the query structure and metadata for calculateBraynAgent.
   */
  public function testCalculateBraynAgentQueryStructure(): void {
    // Create a mock query to test the structure.
    $query = $this->entityTypeManager
      ->getStorage('nanoboost_progress')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->testUser->id())
      ->condition('status', 'completed');

    // Add the same tag and metadata as in the method.
    $query->addTag('module_type_filter');
    $query->addMetaData('query_field_name', 'nbid');
    $query->addMetaData('filter_sz_types', ['module']);

    // Verify the query can be executed.
    $result = $query->count()->execute();
    $this->assertIsInt($result, 'Query should return an integer count.');
  }

  /**
   * Tests that only "module" type shortzzz nodes count for trophies.
   */
  public function testCalculateBraynAgentOnlyModuleTypesCounted(): void {
    // Create 3 completed nanoboost progress for "module" type nodes (indices 0-2).
    $this->createNanoboostProgress(3);

    // Create 2 completed nanoboost progress for "lesson" type nodes (indices 10-11).
    for ($i = 10; $i < 12; $i++) {
      if (isset($this->testNodes[$i])) {
        $progress = NanoBoostProgress::create([
          'uid' => $this->testUser->id(),
          'nbid' => $this->testNodes[$i]->id(),
          'status' => 'completed',
          'data' => [],
          'position' => $i + 1,
        ]);
        $progress->save();
      }
    }

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that only junior and middle trophies were awarded.
    // Even though we have 5 total completed progress entities,
    // only 3 are for "module" type, so only junior (1) and middle (3) should be awarded.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll(\PDO::FETCH_COLUMN);

    $expected_junior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_junior';
    $expected_middle = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_middle';
    $expected_senior = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . '_senior';

    $this->assertContains($expected_junior, $user_trophies, 'Junior trophy should be awarded.');
    $this->assertContains($expected_middle, $user_trophies, 'Middle trophy should be awarded.');
    $this->assertNotContains($expected_senior, $user_trophies, 'Senior trophy should NOT be awarded (only 3 modules, not 12).');
    $this->assertCount(2, $user_trophies, 'Only two trophies should be awarded (lesson type should not count).');
  }

  /**
   * Tests that lesson type shortzzz nodes do not count for trophies.
   */
  public function testCalculateBraynAgentLessonTypesNotCounted(): void {
    // Create 12 completed nanoboost progress for "lesson" type nodes only (indices 10-14).
    for ($i = 10; $i < 15; $i++) {
      if (isset($this->testNodes[$i])) {
        $progress = NanoBoostProgress::create([
          'uid' => $this->testUser->id(),
          'nbid' => $this->testNodes[$i]->id(),
          'status' => 'completed',
          'data' => [],
          'position' => $i + 1,
        ]);
        $progress->save();
      }
    }

    // Process the queue item.
    $this->queueWorker->processItem(['id' => $this->testUser->id()]);

    // Check that no trophies were awarded since lesson types don't count.
    $user_trophies = $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $this->testUser->id())
      ->execute()
      ->fetchAll();

    $this->assertEmpty($user_trophies, 'No trophies should be awarded for lesson type shortzzz nodes.');
  }

}
