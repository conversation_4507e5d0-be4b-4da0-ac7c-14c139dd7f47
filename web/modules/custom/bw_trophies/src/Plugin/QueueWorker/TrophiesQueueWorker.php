<?php

namespace Dr<PERSON>al\bw_trophies\Plugin\QueueWorker;

use <PERSON><PERSON>al\bw_notifications\NotificationService;
use <PERSON><PERSON>al\bw_trophies\Entity\Trophy;
use <PERSON><PERSON>al\Core\Cache\Cache;
use Drupal\Core\Database\Connection;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\Queue\QueueWorkerBase;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\paragraphs\ParagraphInterface;
use Drupal\profile\Entity\Profile;
use Drupal\user\Entity\User;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Processes items from the bw_trophies_queue.
 *
 * @QueueWorker(
 *   id = "bw_trophies_queue",
 *   title = @Translation("Trophies Queue Worker"),
 *   cron = {"time" = 120}
 * )
 */
final class TrophiesQueueWorker extends QueueWorkerBase implements ContainerFactoryPluginInterface {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $connection;

  /**
   * Notification service.
   *
   * @var \Drupal\bw_notifications\NotificationService
   */
  private NotificationService $notification;

  /**
   * The user entity.
   *
   * @var \Drupal\user\Entity\User|null
   */
  private ?User $user = NULL;

  /**
   * The user profile entity.
   *
   * @var \Drupal\user\Entity\UserProfile|null
   */
  private ?Profile $userProfile = NULL;

  /**
   * The user achieved trophies.
   *
   * @var array
   */
  private array $achievedTrophies = [];

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin ID for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Entity type manager.
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   * @param \Drupal\bw_notifications\NotificationService $notification
   *   Notification service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    array $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    Connection $connection,
    NotificationService $notification,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
    $this->connection = $connection;
    $this->notification = $notification;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('database'),
      $container->get('bw_notification'),
    );
  }

  /**
   * {@inheritdoc}
   */
  public function processItem($data) {
    $this->user = $this->entityTypeManager
      ->getStorage('user')
      ->load($data['id']);
    if (!$this->user) {
      return;
    }

    $user_profiles = $this->entityTypeManager
      ->getStorage('profile')
      ->loadByProperties(['uid' => $this->user->id()]);
    $this->userProfile = $user_profiles ? reset($user_profiles) : NULL;
    if (!$this->userProfile) {
      return;
    }

    $this->achievedTrophies = array_flip(
      $this->connection
        ->select('bw_user_trophy', 'ut')
        ->fields('ut', ['trophy'])
        ->condition('uid', $this->user->id())
        ->execute()
        ->fetchAll(\PDO::FETCH_COLUMN)
    );

    if (($types = ($data['types'] ?? []))) {
      foreach ($types as $type) {
        switch ($type) {
          case Trophy::TROPHY_ACTIVITY_BALANCE_MASTER:
            $this->calculateBalanceMaster();
            break;

          case Trophy::TROPHY_ACTIVITY_BRAYN_AGENT:
            $this->calculateBraynAgent();
            break;

          case Trophy::TROPHY_ACTIVITY_DECODER_BRAYN_SCANNER:
            $this->calculateDecoderBraynScanner();
            break;

          case Trophy::TROPHY_ACTIVITY_VECTOR:
            $this->calculateVector();
            break;

          default:
        }
      }

      return;
    }

    $this->calculateAttractor();
    $this->calculateBraynAgent();
    $this->calculateDecoderBraynScanner();
    $this->calculateEmpath();
    $this->calculateTransmitter();
    $this->calculateTrendsetter();

    if (FALSE) {
      $this->calculateAchiever();
      $this->calculateCommunicator();
      $this->calculateContributor();
      $this->calculateCreator();
      $this->calculateExplorer();
      $this->calculateGameChanger();
      $this->calculateInfluencer();
      $this->calculateLifehacker();
      $this->calculateMotivator();
      $this->calculateStrategist();
    }
  }

  /**
   * Calculate achiever.
   */
  private function calculateAchiever() {
    $profile_level = (int) $this->userProfile->field_s_level->value;

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_ACHIEVER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_ACHIEVER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $profile_level >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate attractor.
   */
  private function calculateAttractor() {
    $projects = $this->connection
      ->select('bw_shortzzz_project', 'sp')
      ->fields('sp', ['id', 'initial_likes'])
      ->condition('uid', $this->user->id())
      ->condition('status', ['featured', 'approved'], 'IN')
      ->execute()
      ->fetchAll(\PDO::FETCH_ASSOC);

    $initial_likes = array_sum(array_column($projects, 'initial_likes'));

    $query = $this->connection
      ->select('user__field_student_initial_likes', 'ufsil')
      ->condition('ufsil.entity_id', $this->user->id());
    $query->addExpression('SUM(ufsil.field_student_initial_likes_value)');
    $student_initial_likes_sum = (int) $query->execute()->fetchField();

    $flag_student_like_counts = $this->entityTypeManager
      ->getStorage('flag_student_like')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('entity_id', $this->user->id())
      ->count()
      ->execute();

    $counts_shortzzz_project_likes = 0;
    if (!empty($projects)) {
      $project_ids = array_column($projects, 'id');
      if (!empty($project_ids)) {
        $query = $this->connection
          ->select('bw_flag_counts_shortzzz_project_like', 'f')
          ->condition('f.entity_id', $project_ids, 'IN');
        $query->addExpression('SUM(f.count)');
        $counts_shortzzz_project_likes = (int) $query->execute()->fetchField();
      }
    }

    $like_counts = $flag_student_like_counts + $counts_shortzzz_project_likes + $initial_likes + $student_initial_likes_sum;

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_ATTRACTOR];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_ATTRACTOR . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $like_counts >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate balance master.
   */
  private function calculateBalanceMaster() {
    $trophy_id = Trophy::TROPHY_ACTIVITY_BALANCE_MASTER . "_junior";
    if (!isset($this->achievedTrophies[$trophy_id])) {
      $this->assignTrophyToUser($trophy_id);
    }
  }

  /**
   * Calculate brayn agent.
   */
  private function calculateBraynAgent() {
    // Count completed shortzzz nodes of type "module".
    $query = $this->entityTypeManager
      ->getStorage('nanoboost_progress')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id())
      ->condition('status', 'completed');

    // Add a condition to join with the node entity and filter by type.
    $query->addTag('module_type_filter');
    $query->addMetaData('query_field_name', 'nbid');
    $query->addMetaData('filter_sz_types', ['module']);

    // Execute the count query.
    $completed_shortzzz = $query->count()->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_BRAYN_AGENT];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_BRAYN_AGENT . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $completed_shortzzz >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate communicator.
   */
  private function calculateCommunicator() {
    $user_comments_count = $this->entityTypeManager
      ->getStorage('bw_comment')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id())
      ->count()
      ->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_COMMUNICATOR];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_COMMUNICATOR . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $user_comments_count >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate contributor.
   */
  private function calculateContributor() {
    $bought_lots = $this->entityTypeManager
      ->getStorage('user_buy_history')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id())
      ->condition('type', 'bought_courses', '<>')
      ->count()
      ->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_CONTRIBUTOR];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_CONTRIBUTOR . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $bought_lots >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate creator.
   */
  private function calculateCreator() {
    $uploaded_projects_count = $this->entityTypeManager
      ->getStorage('shortzzz_project')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id())
      ->count()
      ->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_CREATOR];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_CREATOR . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $uploaded_projects_count >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate decoder brayn scanner.
   */
  private function calculateDecoderBraynScanner() {
    $count = $this->entityTypeManager
      ->getStorage('big5_progress')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id())
      ->count()
      ->execute();

    // @todo replace when more test will be added to app.
    if ($count > 0) {
      $count = 1;
    }

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_DECODER_BRAYN_SCANNER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_DECODER_BRAYN_SCANNER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $count >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate empath.
   */
  private function calculateEmpath() {
    $flag_entity_types = [
      'flag_student_like',
      'flag_course_like',
      'flag_shortzzz_like',
      'flag_shortzzz_proj_like',
      'flag_comment_like',
    ];
    $like_counts = 0;
    foreach ($flag_entity_types as $flag_entity_type) {
      $like_counts += $this->entityTypeManager
        ->getStorage($flag_entity_type)
        ->getQuery()
        ->accessCheck(FALSE)
        ->condition('user_id', $this->user->id())
        ->count()
        ->execute();
    }

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_EMPATH];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_EMPATH . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $like_counts >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate explorer.
   */
  private function calculateExplorer() {
    // Count completed shortzzz nodes of type "module".
    $query = $this->entityTypeManager
      ->getStorage('nanoboost_progress')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id())
      ->condition('status', 'completed');

    // Add a condition to join with the node entity and filter by type.
    $query->addTag('module_type_filter');
    $query->addMetaData('query_field_name', 'nbid');
    $query->addMetaData('filter_sz_types', ['module']);

    // Execute the count query.
    $completed_shortzzz = $query->count()->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_EXPLORER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_EXPLORER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $completed_shortzzz >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate game-changer.
   */
  private function calculateGameChanger() {
    $query = $this->connection
      ->select('bw_user_buy_history', 'bw_ubh')
      ->condition('uid', $this->user->id());
    $query->addExpression('SUM(paid_neurons)');
    $paid_neurons = (int) $query->execute()->fetchField();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_GAME_CHANGER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_GAME_CHANGER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $paid_neurons >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate influencer.
   */
  private function calculateInfluencer() {
    $query = $this->connection
      ->select('user__field_student_initial_follows', 'uf')
      ->condition('uf.entity_id', $this->user->id());
    $query->addExpression('SUM(uf.field_student_initial_follows_value)');
    $student_initial_follows = (int) $query->execute()->fetchField();

    $follow_counts = $this->entityTypeManager
      ->getStorage('flag_student_follow')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('entity_id', $this->user->id())
      ->count()
      ->execute();

    $follow_counts = $follow_counts + $student_initial_follows;

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_INFLUENCER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_INFLUENCER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $follow_counts >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate lifehacker.
   */
  private function calculateLifehacker() {
    $paid_axons = 0;
    $additional_data = $this->userProfile->field_s_additional_data->isEmpty()
      ? []
      : unserialize($this->userProfile->field_s_additional_data->value, ['allowed_classes' => FALSE]);
    foreach ($additional_data['bought_courses'] ?? [] as $bought_course_datum) {
      if ('axons' === $bought_course_datum['type']) {
        $paid_axons += $bought_course_datum['amount'];
      }
    }

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_LIFEHACKER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_LIFEHACKER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $paid_axons >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate motivator.
   */
  private function calculateMotivator() {
    $video_ids = array_map(
      fn ($uri) => (int) explode('/', $uri)[3],
      $this->connection
        ->select('bw_shortzzz_project', 'sp')
        ->fields('sp', ['uri'])
        ->condition('uid', $this->user->id())
        ->condition('status', ['featured', 'approved'], 'IN')
        ->execute()
        ->fetchAll(\PDO::FETCH_COLUMN),
    );
    if (empty($video_ids)) {
      return;
    }

    $user_comments_count = (int) $this->entityTypeManager
      ->getStorage('bw_comment')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('uid', $this->user->id(), '<>')
      ->condition('video_id', $video_ids, 'IN')
      ->count()
      ->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_MOTIVATOR];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_MOTIVATOR . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $user_comments_count >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate strategist.
   */
  private function calculateStrategist() {
    $added_favourites_counts = $this->entityTypeManager
      ->getStorage('flag_shortzzz_favourite')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('user_id', $this->user->id())
      ->count()
      ->execute();

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_STRATEGIST];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_STRATEGIST . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $added_favourites_counts >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate transmitter.
   */
  private function calculateTransmitter() {
    $flag_entity_types = [
      'flag_student_share',
      'flag_course_share',
      'flag_shortzzz_share',
    ];
    $share_counts = 0;
    foreach ($flag_entity_types as $flag_entity_type) {
      $share_counts += $this->entityTypeManager
        ->getStorage($flag_entity_type)
        ->getQuery()
        ->accessCheck(FALSE)
        ->condition('user_id', $this->user->id())
        ->count()
        ->execute();
    }

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_TRANSMITTER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_TRANSMITTER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $share_counts >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate trendsetter.
   */
  private function calculateTrendsetter() {
    $query = $this->connection
      ->select('user__field_student_initial_views', 'uv')
      ->condition('uv.entity_id', $this->user->id());
    $query->addExpression('SUM(uv.field_student_initial_views_value)');
    $student_initial_views = (int) $query->execute()->fetchField();

    $view_counts = $this->entityTypeManager
      ->getStorage('flag_student_view')
      ->getQuery()
      ->accessCheck(FALSE)
      ->condition('entity_id', $this->user->id())
      ->count()
      ->execute();

    $counts_shortzzz_project_views = 0;
    $projects = $this->connection
      ->select('bw_shortzzz_project', 'sp')
      ->fields('sp', ['id'])
      ->condition('uid', $this->user->id())
      ->condition('status', ['featured', 'approved'], 'IN')
      ->execute()
      ->fetchAll(\PDO::FETCH_ASSOC);

    if (!empty($projects)) {
      $project_ids = array_column($projects, 'id');
      if (!empty($project_ids)) {
        $query = $this->connection
          ->select('bw_flag_counts_shortzzz_project_view', 'f')
          ->condition('f.entity_id', $project_ids, 'IN');
        $query->addExpression('SUM(f.count)');
        $counts_shortzzz_project_views = (int) $query->execute()->fetchField();
      }
    }

    $view_counts = $view_counts + $student_initial_views + $counts_shortzzz_project_views;

    $goals = Trophy::TROPHY_ACTIVITY_TYPE_TO_LEVEL_GOALS_MAPPING[Trophy::TROPHY_ACTIVITY_TRENDSETTER];
    foreach ($goals as $level => $goal) {
      $trophy_id = Trophy::TROPHY_ACTIVITY_TRENDSETTER . "_{$level}";
      if (!isset($this->achievedTrophies[$trophy_id]) && $view_counts >= $goal) {
        $this->assignTrophyToUser($trophy_id);
      }
    }
  }

  /**
   * Calculate vector.
   */
  private function calculateVector() {
    $trophy_id = Trophy::TROPHY_ACTIVITY_VECTOR . "_junior";
    if (!isset($this->achievedTrophies[$trophy_id])) {
      $this->assignTrophyToUser($trophy_id);
    }
  }

  /**
   * Assign trophy to user.
   *
   * @param string $trophy_id
   *   The id of the trophy.
   */
  private function assignTrophyToUser(string $trophy_id) {
    /** @var \Drupal\bw_trophies\Entity\Trophy $trophy */
    $trophy = $this->entityTypeManager
      ->getStorage('trophy')
      ->load($trophy_id);
    if (!$trophy) {
      return;
    }

    $transaction = $this->connection->startTransaction();
    try {
      $axons = $neurons = 0;
      if (($rewards = $trophy->get('rewards_id')->entity) instanceof ParagraphInterface) {
        // Reload profile to mitigate race condition on updating rewards.
        $user_profiles = $this->entityTypeManager
          ->getStorage('profile')
          ->loadByProperties(['uid' => $this->user->id()]);
        /** @var \Drupal\profile\Entity\Profile $user_profile */
        $user_profile = $user_profiles ? reset($user_profiles) : NULL;

        $axons = $rewards->get('field_axons')->value ?? 0;
        $neurons = $rewards->get('field_neurons')->value ?? 0;
        $user_profile->set(
          'field_s_axons',
          $user_profile->get('field_s_axons')->value + $axons
        );
        $user_profile->set(
          'field_s_neurons',
          $user_profile->get('field_s_neurons')->value + $neurons
        );
        $user_profile->save();
      }

      $this->entityTypeManager
        ->getStorage('user_trophy')
        ->create([
          'uid' => $this->user->id(),
          'trophy' => $trophy->id(),
          'date_acquired' => (new \DateTime())->getTimestamp(),
        ])
        ->save();
    }
    catch (\Throwable $e) {
      $transaction->rollback();

      throw $e;
    }

    unset($transaction);

    Cache::invalidateTags(
      ["user_trophies:{$this->user->id()}"]
    );

    $this->notification->send(
      $this->userProfile,
      [
        'contents' => [
          'en' => new TranslatableMarkup(
            '@title unlocked!',
            ['@title' => $trophy->label()],
          ),
        ],
        'include_external_user_ids' => [
          $this->user->uuid(),
        ],
        'data' => array_filter([
          'type' => 'trophy_rewards',
          'trophy' => $trophy->id(),
          'title' => $trophy->label(),
          'rewards' => array_filter([
            'axons' => $axons,
            'neurons' => $neurons,
          ]),
          'icon' => (
            $trophy->icon_fid->entity
            && ($icon_uri = $trophy->icon_fid?->entity?->field_media_image_1?->entity->createFileUrl())
          )
            ? $icon_uri
            : NULL,
          'icon_grey' => (
            $trophy->icon_gray_fid->entity
            && ($icon_gray_uri = $trophy->icon_gray_fid?->entity?->field_media_image_1?->entity->createFileUrl())
          )
            ? $icon_gray_uri
            : NULL,
        ]),
      ],
    );
  }

}
