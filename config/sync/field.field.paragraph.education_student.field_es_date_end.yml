uuid: 568e5460-5f04-4913-b6a4-a91bff1f719e
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_es_date_end
    - paragraphs.paragraphs_type.education_student
  module:
    - datetime
id: paragraph.education_student.field_es_date_end
field_name: field_es_date_end
entity_type: paragraph
bundle: education_student
label: 'End date'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
