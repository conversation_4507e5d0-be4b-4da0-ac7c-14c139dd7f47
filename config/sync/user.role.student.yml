uuid: b2aeb103-996c-4482-8a3c-f74046afc74b
langcode: en
status: true
dependencies:
  config:
    - rest.resource.bw_audio_files
    - rest.resource.bw_avatart_files
    - rest.resource.bw_big_five
    - rest.resource.bw_branch
    - rest.resource.bw_branch_stage_purchase
    - rest.resource.bw_branches
    - rest.resource.bw_coming_soon
    - rest.resource.bw_comment_user_tag
    - rest.resource.bw_comments
    - rest.resource.bw_content_flags
    - rest.resource.bw_course_flags
    - rest.resource.bw_course_projects
    - rest.resource.bw_current_user_course_projects
    - rest.resource.bw_feed
    - rest.resource.bw_flagging
    - rest.resource.bw_lobby_banner
    - rest.resource.bw_market
    - rest.resource.bw_nanoboost
    - rest.resource.bw_nanoboost_progress
    - rest.resource.bw_nanoboost_purchase
    - rest.resource.bw_recommended_courses
    - rest.resource.bw_scpecial_offers
    - rest.resource.bw_shortzzz_project_flags
    - rest.resource.bw_shortzzz_project_predefined_url
    - rest.resource.bw_shortzzz_projects
    - rest.resource.bw_user
    - rest.resource.bw_user_activity
    - rest.resource.bw_user_big5_progress
    - rest.resource.bw_user_bigfive
    - rest.resource.bw_user_branch_progress
    - rest.resource.bw_user_course_progress
    - rest.resource.bw_user_course_progress_access_test
    - rest.resource.bw_user_course_progress_buy
    - rest.resource.bw_user_course_progress_buy_so
    - rest.resource.bw_user_course_project
    - rest.resource.bw_user_course_projects
    - rest.resource.bw_user_courses
    - rest.resource.bw_user_delete
    - rest.resource.bw_user_email_verify
    - rest.resource.bw_user_flags
    - rest.resource.bw_user_market
    - rest.resource.bw_user_me
    - rest.resource.bw_user_nfr_project_by_nfr_id_resource
    - rest.resource.bw_user_notifications
    - rest.resource.bw_user_resend_email_verify
    - rest.resource.bw_user_rewards
    - rest.resource.bw_user_shortzzz_project
    - rest.resource.bw_user_spend_rewards
    - rest.resource.bw_user_tokens
    - rest.resource.bw_user_trophies
    - rest.resource.bw_user_update
    - rest.resource.bw_video_component_flags
  module:
    - file
    - profile
    - rest
id: student
label: Student
weight: -8
is_admin: null
permissions:
  - 'create student profile'
  - 'delete own files'
  - 'restful delete bw_comments'
  - 'restful delete bw_comments'
  - 'restful delete bw_flagging'
  - 'restful delete bw_user_delete'
  - 'restful get bw_audio_files'
  - 'restful get bw_avatart_files'
  - 'restful get bw_big_five'
  - 'restful get bw_branch'
  - 'restful get bw_branches'
  - 'restful get bw_coming_soon'
  - 'restful get bw_comment_user_tag'
  - 'restful get bw_content_flags'
  - 'restful get bw_course_flags'
  - 'restful get bw_course_projects'
  - 'restful get bw_current_user_course_projects'
  - 'restful get bw_feed'
  - 'restful get bw_lobby_banner'
  - 'restful get bw_market'
  - 'restful get bw_nanoboost'
  - 'restful get bw_nanoboost_progress'
  - 'restful get bw_recommended_courses'
  - 'restful get bw_scpecial_offers'
  - 'restful get bw_shortzzz_project_flags'
  - 'restful get bw_shortzzz_project_predefined_url'
  - 'restful get bw_shortzzz_projects'
  - 'restful get bw_user'
  - 'restful get bw_user_bigfive'
  - 'restful get bw_user_branch_progress'
  - 'restful get bw_user_course_progress'
  - 'restful get bw_user_course_project'
  - 'restful get bw_user_course_projects'
  - 'restful get bw_user_courses'
  - 'restful get bw_user_flags'
  - 'restful get bw_user_market'
  - 'restful get bw_user_me'
  - 'restful get bw_user_nfr_project_by_nfr_id_resource'
  - 'restful get bw_user_notifications'
  - 'restful get bw_user_shortzzz_project'
  - 'restful get bw_user_tokens'
  - 'restful get bw_user_trophies'
  - 'restful get bw_video_component_flags'
  - 'restful patch bw_user_spend_rewards'
  - 'restful patch bw_user_update'
  - 'restful post bw_branch_stage_purchase'
  - 'restful post bw_comments'
  - 'restful post bw_flagging'
  - 'restful post bw_nanoboost_purchase'
  - 'restful post bw_user_activity'
  - 'restful post bw_user_big5_progress'
  - 'restful post bw_user_course_progress_access_test'
  - 'restful post bw_user_course_progress_buy'
  - 'restful post bw_user_course_progress_buy_so'
  - 'restful post bw_user_course_project'
  - 'restful post bw_user_email_verify'
  - 'restful post bw_user_market'
  - 'restful post bw_user_resend_email_verify'
  - 'restful post bw_user_rewards'
  - 'restful post bw_user_shortzzz_project'
  - 'restful put bw_nanoboost_progress'
  - 'restful put bw_user_course_progress'
