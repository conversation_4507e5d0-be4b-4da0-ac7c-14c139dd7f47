uuid: 87c542dc-eff2-4528-b6d9-96fac34f745c
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_preview_image
    - paragraphs.paragraphs_type.dream_job
  module:
    - image
id: paragraph.dream_job.field_preview_image
field_name: field_preview_image
entity_type: paragraph
bundle: dream_job
label: 'Preview image'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg webp'
  max_filesize: '500 KB'
  max_resolution: ''
  min_resolution: ''
  alt_field: false
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
