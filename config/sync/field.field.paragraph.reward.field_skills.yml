uuid: 5efa897b-4dcf-4d34-a3a4-896fe67c4ec8
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_skills
    - paragraphs.paragraphs_type.reward
    - paragraphs.paragraphs_type.skill_course
  module:
    - entity_reference_revisions
id: paragraph.reward.field_skills
field_name: field_skills
entity_type: paragraph
bundle: reward
label: Skills
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      skill_course: skill_course
    negate: 0
    target_bundles_drag_drop:
      access_axons:
        weight: 55
        enabled: false
      access_money:
        weight: 56
        enabled: false
      access_neurons:
        weight: 57
        enabled: false
      access_test:
        weight: 58
        enabled: false
      access_test_question:
        weight: 59
        enabled: false
      ai_game_chat:
        weight: 60
        enabled: false
      ai_text_chat:
        weight: 61
        enabled: false
      ai_voice_chat:
        weight: 62
        enabled: false
      animation:
        weight: 63
        enabled: false
      big5:
        weight: 64
        enabled: false
      big5_results:
        weight: 65
        enabled: false
      bubble_quiz:
        weight: 66
        enabled: false
      certifications:
        weight: 67
        enabled: false
      course:
        weight: 68
        enabled: false
      dream_job:
        weight: 69
        enabled: false
      education_student:
        weight: 70
        enabled: false
      explanatotry_note:
        weight: 71
        enabled: false
      factor:
        weight: 72
        enabled: false
      feed_alpha_gen:
        weight: 74
        enabled: false
      feed_big5:
        weight: 75
        enabled: false
      feed_coming_soon:
        weight: 76
        enabled: false
      feed_course:
        weight: 77
        enabled: false
      feed_course_project:
        weight: 78
        enabled: false
      feed_nano_boost:
        weight: 79
        enabled: false
      feed_nano_boost_project:
        weight: 80
        enabled: false
      feed_ugc_by_brayn:
        weight: 81
        enabled: false
      feeds:
        weight: 73
        enabled: false
      infographic:
        weight: 82
        enabled: false
      infographic_slider:
        weight: 83
        enabled: false
      infographic_slider_images:
        weight: 84
        enabled: false
      lobby_banner:
        weight: 85
        enabled: false
      multioption_qustion:
        weight: 86
        enabled: false
      offer:
        weight: 87
        enabled: false
      onboarding_special_offers:
        weight: 88
        enabled: false
      quiz_crossword:
        weight: 89
        enabled: false
      quiz_crossword_letter:
        weight: 90
        enabled: false
      quiz_image:
        weight: 91
        enabled: false
      quiz_image_question:
        weight: 92
        enabled: false
      quiz_tinder_mechanics:
        weight: 93
        enabled: false
      quiz_tinder_mechanics_question:
        weight: 94
        enabled: false
      quiz_video:
        weight: 95
        enabled: false
      quiz_video_question:
        weight: 96
        enabled: false
      radar:
        weight: 97
        enabled: false
      results:
        weight: 98
        enabled: false
      reward:
        weight: 99
        enabled: false
      set_smart_goal:
        weight: 100
        enabled: false
      sign_up:
        weight: 101
        enabled: false
      skill_course:
        weight: 102
        enabled: true
      skill_student:
        weight: 103
        enabled: false
      smart:
        weight: 104
        enabled: false
      smart_goal:
        weight: 105
        enabled: false
      video:
        weight: 106
        enabled: false
      video_task:
        weight: 107
        enabled: false
      work_student:
        weight: 108
        enabled: false
field_type: entity_reference_revisions
