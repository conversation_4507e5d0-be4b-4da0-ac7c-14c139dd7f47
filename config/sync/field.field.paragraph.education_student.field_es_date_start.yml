uuid: b58b456d-00ef-4569-8e5a-c619c72ee8d8
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_es_date_start
    - paragraphs.paragraphs_type.education_student
  module:
    - datetime
id: paragraph.education_student.field_es_date_start
field_name: field_es_date_start
entity_type: paragraph
bundle: education_student
label: 'Start date'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
